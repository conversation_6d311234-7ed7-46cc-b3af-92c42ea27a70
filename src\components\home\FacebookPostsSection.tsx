import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { Facebook, ExternalLink, Loader2 } from 'lucide-react';
import VideoModal from '../common/VideoModal';

// Facebook SDK types
declare global {
  interface Window {
    FB: {
      init: (params: any) => void;
      XFBML: {
        parse: () => void;
      };
    };
  }
}

const FacebookPostsSection: React.FC = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [fbLoaded, setFbLoaded] = useState(false);
  const [loadError, setLoadError] = useState(false);
  const [showVideoModal, setShowVideoModal] = useState(false);
  const [selectedVideoUrl, setSelectedVideoUrl] = useState('');
  const [selectedVideoTitle, setSelectedVideoTitle] = useState('');

  // Facebook Page URL - St. Louis Demonstration JHS Official Page
  const facebookPageUrl = "https://www.facebook.com/stlouisdemojhs";

  useEffect(() => {
    // Load Facebook SDK
    const loadFacebookSDK = () => {
      if (window.FB) {
        setFbLoaded(true);
        setIsLoading(false);
        return;
      }

      // Create Facebook SDK script
      const script = document.createElement('script');
      script.src = 'https://connect.facebook.net/en_US/sdk.js#xfbml=1&version=v18.0';
      script.async = true;
      script.defer = true;
      script.crossOrigin = 'anonymous';

      script.onload = () => {
        if (window.FB) {
          window.FB.init({
            xfbml: true,
            version: 'v18.0'
          });
          setFbLoaded(true);
          setIsLoading(false);
        }
      };

      script.onerror = () => {
        setIsLoading(false);
        setLoadError(true);
      };

      document.head.appendChild(script);
    };

    loadFacebookSDK();

    // Cleanup
    return () => {
      // Remove script if component unmounts
      const existingScript = document.querySelector('script[src*="connect.facebook.net"]');
      if (existingScript) {
        existingScript.remove();
      }
    };
  }, []);

  // Refresh Facebook widgets when loaded
  useEffect(() => {
    if (fbLoaded && window.FB) {
      setTimeout(() => {
        window.FB.XFBML.parse();
      }, 100);
    }
  }, [fbLoaded]);

  // Handle video modal
  const handleVideoClick = (videoUrl: string, title: string = 'Facebook Video') => {
    setSelectedVideoUrl(videoUrl);
    setSelectedVideoTitle(title);
    setShowVideoModal(true);
  };

  const handleCloseVideoModal = () => {
    setShowVideoModal(false);
    setSelectedVideoUrl('');
    setSelectedVideoTitle('');
  };

  // Determine video platform
  const getVideoPlatform = (url: string): 'facebook' | 'youtube' | 'vimeo' | 'other' => {
    if (url.includes('facebook.com')) return 'facebook';
    if (url.includes('youtube.com') || url.includes('youtu.be')) return 'youtube';
    if (url.includes('vimeo.com')) return 'vimeo';
    return 'other';
  };

  // Add click handlers to Facebook posts after they load
  useEffect(() => {
    if (fbLoaded && window.FB) {
      setTimeout(() => {
        // Method 1: Try to intercept clicks on the Facebook iframe
        const fbIframes = document.querySelectorAll('iframe[src*="facebook.com"]');

        fbIframes.forEach((iframe) => {
          try {
            // Add overlay to capture clicks (for cross-origin iframe)
            const overlay = document.createElement('div');
            overlay.style.position = 'absolute';
            overlay.style.top = '0';
            overlay.style.left = '0';
            overlay.style.width = '100%';
            overlay.style.height = '100%';
            overlay.style.zIndex = '10';
            overlay.style.pointerEvents = 'none'; // Allow normal interaction
            overlay.style.background = 'transparent';

            // Add to iframe parent
            const parent = iframe.parentElement;
            if (parent) {
              parent.style.position = 'relative';
              parent.appendChild(overlay);
            }
          } catch (error) {
            console.log('Cannot access iframe content due to CORS policy');
          }
        });

        // Method 2: Add global click listener for video URLs
        document.addEventListener('click', (e) => {
          const target = e.target as HTMLElement;
          const link = target.closest('a[href*="facebook.com/watch"], a[href*="youtube.com"], a[href*="youtu.be"]');

          if (link) {
            const anchor = link as HTMLAnchorElement;
            // Only intercept if it's within our Facebook section
            const fbSection = anchor.closest('.fb-page');
            if (fbSection) {
              e.preventDefault();
              e.stopPropagation();
              handleVideoClick(anchor.href, anchor.textContent || 'Facebook Video');
            }
          }
        });


      }, 3000); // Wait for Facebook content to fully load
    }
  }, [fbLoaded]);

  return (
    <section className="py-16 sm:py-20 bg-gradient-to-br from-white via-gray-50 to-blue-50 relative overflow-hidden">
      {/* Enhanced Background Pattern */}
      <div className="absolute inset-0">
        {/* Subtle gradient orbs */}
        <div className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-r from-yellow-200 to-green-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse"></div>
        <div className="absolute bottom-0 right-0 w-96 h-96 bg-gradient-to-r from-green-200 to-blue-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-gradient-to-r from-blue-200 to-yellow-200 rounded-full mix-blend-multiply filter blur-xl opacity-25 animate-pulse delay-500"></div>

        {/* Floating particles */}
        <div className="absolute top-20 left-20 w-2 h-2 bg-yellow-400 rounded-full opacity-60 animate-bounce delay-300"></div>
        <div className="absolute top-40 right-32 w-3 h-3 bg-green-400 rounded-full opacity-40 animate-bounce delay-700"></div>
        <div className="absolute bottom-32 left-40 w-2 h-2 bg-blue-400 rounded-full opacity-50 animate-bounce delay-1000"></div>
        <div className="absolute bottom-20 right-20 w-4 h-4 bg-yellow-300 rounded-full opacity-30 animate-bounce delay-1500"></div>

        {/* Grid pattern overlay */}
        <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>

        {/* Subtle noise texture */}
        <div className="absolute inset-0 bg-noise-pattern opacity-10"></div>
      </div>

      {/* Light overlay */}
      <div className="absolute inset-0 bg-gradient-to-b from-transparent via-white/10 to-transparent backdrop-blur-[0.5px]"></div>

      <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <div className="flex items-center justify-center gap-3 mb-6">
            <div className="w-14 h-14 bg-gradient-to-r from-blue-600 to-blue-700 rounded-full flex items-center justify-center shadow-lg shadow-blue-500/25 ring-4 ring-blue-100">
              <Facebook size={28} className="text-white" />
            </div>
            <h2 className="text-3xl sm:text-4xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-green-700 bg-clip-text text-transparent">
              Latest Updates
            </h2>
          </div>
          <p className="text-lg text-gray-700 max-w-2xl mx-auto leading-relaxed">
            Stay connected with our school community. Follow our latest news, events, and achievements on Facebook.
          </p>

          {/* Decorative elements */}
          <div className="flex items-center justify-center gap-2 mt-6">
            <div className="w-12 h-0.5 bg-gradient-to-r from-transparent to-yellow-400 rounded-full"></div>
            <div className="w-2 h-2 bg-yellow-400 rounded-full animate-pulse"></div>
            <div className="w-8 h-0.5 bg-yellow-400 rounded-full"></div>
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse delay-300"></div>
            <div className="w-12 h-0.5 bg-gradient-to-l from-transparent to-green-400 rounded-full"></div>
          </div>
        </motion.div>

        {/* Facebook Posts Container */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="w-full"
        >
          {/* Loading State */}
          {isLoading && !loadError && (
            <div className="flex items-center justify-center py-16">
              <div className="text-center bg-white/80 backdrop-blur-md rounded-2xl p-8 border border-gray-200 shadow-lg">
                <div className="relative">
                  <Loader2 size={48} className="text-blue-600 animate-spin mx-auto mb-4" />
                  <div className="absolute inset-0 bg-gradient-to-r from-yellow-400 to-green-400 rounded-full opacity-20 animate-ping"></div>
                </div>
                <p className="text-gray-700 font-medium">Loading Facebook posts...</p>
                <div className="flex items-center justify-center gap-1 mt-3">
                  <div className="w-2 h-2 bg-yellow-400 rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-bounce delay-100"></div>
                  <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce delay-200"></div>
                </div>
              </div>
            </div>
          )}

          {/* Error State - Fallback */}
          {loadError && (
            <div className="bg-white/90 backdrop-blur-md rounded-2xl shadow-xl overflow-hidden border border-gray-200 p-8">
              <div className="text-center">
                <div className="w-20 h-20 bg-gradient-to-r from-blue-600 to-blue-700 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg shadow-blue-500/25 ring-4 ring-blue-100">
                  <Facebook size={36} className="text-white" />
                </div>
                <h3 className="text-2xl font-bold bg-gradient-to-r from-gray-900 to-blue-800 bg-clip-text text-transparent mb-3">
                  Connect with us on Facebook
                </h3>
                <p className="text-gray-700 mb-8 leading-relaxed max-w-md mx-auto">
                  Stay updated with our latest news, events, and school activities by following our official Facebook page.
                </p>
                <a
                  href={facebookPageUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-bold rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-blue-500/25 ring-2 ring-blue-100"
                >
                  <Facebook size={22} />
                  <span>Visit Our Facebook Page</span>
                  <ExternalLink size={18} />
                </a>

                {/* Decorative elements */}
                <div className="flex items-center justify-center gap-2 mt-6">
                  <div className="w-8 h-0.5 bg-gradient-to-r from-transparent to-yellow-400 rounded-full"></div>
                  <div className="w-1.5 h-1.5 bg-yellow-400 rounded-full animate-pulse"></div>
                  <div className="w-6 h-0.5 bg-yellow-400 rounded-full"></div>
                  <div className="w-1.5 h-1.5 bg-green-400 rounded-full animate-pulse delay-300"></div>
                  <div className="w-8 h-0.5 bg-gradient-to-l from-transparent to-green-400 rounded-full"></div>
                </div>
              </div>
            </div>
          )}

          {/* Facebook Page Plugin - Neon Glow Container */}
          {!isLoading && !loadError && (
            <div className="flex justify-center">
              {/* Desktop Layout - Centered with Neon Glow */}
              <div className="hidden lg:block">
                <div className="relative">
                  {/* Neon Glow Effects */}
                  <div className="absolute -inset-1 bg-gradient-to-r from-yellow-400 via-green-400 to-yellow-400 rounded-2xl blur-sm opacity-75 animate-pulse"></div>
                  <div className="absolute -inset-0.5 bg-gradient-to-r from-green-400 via-yellow-400 to-green-400 rounded-2xl blur-xs opacity-50 animate-pulse delay-300"></div>

                  {/* Main Container */}
                  <div className="relative bg-white rounded-2xl shadow-2xl overflow-hidden border-2 border-white">
                    <div
                      className="fb-page"
                      data-href={facebookPageUrl}
                      data-tabs="timeline"
                      data-width="800"
                      data-height="700"
                      data-small-header="false"
                      data-adapt-container-width="false"
                      data-hide-cover="false"
                      data-show-facepile="true"
                      data-show-posts="true"
                      data-lazy="true"
                    >
                      <blockquote cite={facebookPageUrl} className="fb-xfbml-parse-ignore">
                        <a href={facebookPageUrl} className="text-blue-600 hover:text-blue-800 transition-colors">
                          Visit our Facebook page
                        </a>
                      </blockquote>
                    </div>
                  </div>
                </div>
              </div>

              {/* Mobile/Tablet Layout - Centered with Subtle Glow */}
              <div className="block lg:hidden w-full max-w-md mx-auto">
                <div className="relative">
                  {/* Mobile Neon Glow Effects */}
                  <div className="absolute -inset-1 bg-gradient-to-r from-yellow-400 via-green-400 to-yellow-400 rounded-xl blur-sm opacity-60 animate-pulse"></div>
                  <div className="absolute -inset-0.5 bg-gradient-to-r from-green-400 via-yellow-400 to-green-400 rounded-xl blur-xs opacity-40 animate-pulse delay-300"></div>

                  {/* Mobile Container */}
                  <div className="relative bg-white rounded-xl shadow-xl overflow-hidden border-2 border-white">
                    <div
                      className="fb-page w-full"
                      data-href={facebookPageUrl}
                      data-tabs="timeline"
                      data-width="400"
                      data-height="600"
                      data-small-header="true"
                      data-adapt-container-width="true"
                      data-hide-cover="false"
                      data-show-facepile="false"
                      data-show-posts="true"
                      data-lazy="true"
                    >
                      <blockquote cite={facebookPageUrl} className="fb-xfbml-parse-ignore">
                        <a href={facebookPageUrl} className="text-blue-600 hover:text-blue-800 transition-colors">
                          Visit our Facebook page
                        </a>
                      </blockquote>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Visit Facebook Page Button */}
          {!loadError && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="text-center mt-8"
          >
            <a
              href={facebookPageUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-bold rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-blue-500/25 ring-2 ring-blue-100"
            >
              <Facebook size={22} />
              <span>Visit Our Facebook Page</span>
              <ExternalLink size={18} />
            </a>
          </motion.div>
          )}
        </motion.div>
      </div>

      {/* Video Modal */}
      {selectedVideoUrl && (
        <VideoModal
          isOpen={showVideoModal}
          onClose={handleCloseVideoModal}
          videoUrl={selectedVideoUrl}
          title={selectedVideoTitle}
          description="Video from St. Louis Demonstration JHS Facebook page"
          platform={getVideoPlatform(selectedVideoUrl)}
        />
      )}
    </section>
  );
};

export default FacebookPostsSection;
