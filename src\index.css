@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Nunito:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
  @keyframes shimmer {
    0% {
      transform: translateX(-100%);
    }
    100% {
      transform: translateX(100%);
    }
  }

  @keyframes shimmer-wave {
    0% {
      transform: translateX(-100%) skewX(-12deg);
    }
    100% {
      transform: translateX(200%) skewX(-12deg);
    }
  }

  .animate-shimmer {
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite linear;
  }

  .animate-shimmer-wave {
    animation: shimmer-wave 2s infinite linear;
  }
}

/* Cute mobile-friendly styles */
.cute-font {
  font-family: 'Nunito', sans-serif;
}

.sharp-container {
  border-radius: 0.375rem; /* 6px - sharp but not harsh */
}

.mini-container {
  padding: 0.75rem; /* 12px */
  margin: 0.5rem 0; /* 8px vertical */
}

/* Neon Red Glow Animation for Donate Buttons */
@keyframes neon-glow {
  0%, 100% {
    box-shadow:
      0 0 20px rgba(239, 68, 68, 0.6),
      0 0 40px rgba(239, 68, 68, 0.4),
      0 0 60px rgba(239, 68, 68, 0.2),
      inset 0 0 20px rgba(239, 68, 68, 0.1);
  }
  50% {
    box-shadow:
      0 0 30px rgba(239, 68, 68, 0.8),
      0 0 60px rgba(239, 68, 68, 0.6),
      0 0 90px rgba(239, 68, 68, 0.4),
      inset 0 0 30px rgba(239, 68, 68, 0.2);
  }
}

@keyframes neon-pulse {
  0%, 100% {
    text-shadow:
      0 0 5px rgba(255, 255, 255, 0.9),
      0 0 10px rgba(255, 255, 255, 0.7),
      0 0 15px rgba(239, 68, 68, 0.3);
  }
  50% {
    text-shadow:
      0 0 8px rgba(255, 255, 255, 1),
      0 0 15px rgba(255, 255, 255, 0.8),
      0 0 20px rgba(239, 68, 68, 0.4);
  }
}

.neon-red-glow {
  animation: neon-glow 2s ease-in-out infinite alternate;
}

.neon-text-glow {
  animation: neon-pulse 2s ease-in-out infinite alternate;
}

/* Simple loading animations only */

@layer base {
  html {
    font-feature-settings: "cv02", "cv03", "cv04", "cv11";
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  h1, h2, h3, h4, h5, h6 {
    letter-spacing: -0.025em;
  }

  p {
    letter-spacing: -0.01em;
  }
}

@keyframes shimmer {
  0% {
    background-position: 0% 0%, 10% 10%, 20% 20%, 30% 30%, 40% 40%, 50% 50%, 60% 60%, 70% 70%, 80% 80%, 90% 90%, 100% 100%, 0% 0%, 0% 0%;
  }
  100% {
    background-position: 100% 100%, 90% 90%, 80% 80%, 70% 70%, 60% 60%, 50% 50%, 40% 40%, 30% 30%, 20% 20%, 10% 10%, 0% 0%, 100% 100%, 100% 100%;
  }
}

@layer utilities {
  .glass {
    @apply bg-black/30 backdrop-blur-md border border-white/10;
  }

  .glass-card {
    @apply bg-zinc-900/50 backdrop-blur-md border border-white/10;
  }

  .glass-dark {
    @apply bg-zinc-950/80 backdrop-blur-xl border border-white/10;
    backdrop-filter: blur(20px) saturate(180%);
    -webkit-backdrop-filter: blur(20px) saturate(180%);
  }

  .glass-homepage {
    @apply bg-black/85 backdrop-blur-2xl border-b border-white/20;
    backdrop-filter: blur(24px) saturate(180%);
    -webkit-backdrop-filter: blur(24px) saturate(180%);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.05);
  }
}

:root {
  --background: 240 10% 3.9%;
  --foreground: 0 0% 98%;
  --card: 240 10% 3.9%;
  --card-foreground: 0 0% 98%;
  --popover: 240 10% 3.9%;
  --popover-foreground: 0 0% 98%;
  --primary: 0 0% 98%;
  --primary-foreground: 240 5.9% 10%;
  --secondary: 240 3.7% 15.9%;
  --secondary-foreground: 0 0% 98%;
  --muted: 240 3.7% 15.9%;
  --muted-foreground: 240 5% 64.9%;
  --accent: 240 3.7% 15.9%;
  --accent-foreground: 0 0% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 0 0% 98%;
  --border: 240 3.7% 15.9%;
  --input: 240 3.7% 15.9%;
  --ring: 240 4.9% 83.9%;
}

body {
  @apply bg-black text-white;
  /* Prevent white flash/edge blinking and optimize performance */
  background-color: #000000 !important;
  overflow-x: hidden !important;
  max-width: 100vw;
  /* Performance optimizations */
  transform: translateZ(0);
  backface-visibility: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Prevent white edges and layout shifts */
html {
  background-color: #000000 !important;
  overflow-x: hidden !important;
  max-width: 100vw;
  /* Smooth scrolling performance */
  scroll-behavior: smooth;
}

/* Smooth header transitions */
header {
  will-change: background-color, backdrop-filter, box-shadow;
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* ElevenLabs ConvAI Widget Styling */
.convai-widget-container {
  /* Ensure the widget is visible and properly positioned */
  position: relative;
  z-index: 50;
}

/* Style the elevenlabs-convai custom element */
elevenlabs-convai {
  display: block !important;
  position: relative !important;
  z-index: 50 !important;
  /* Ensure minimum dimensions */
  min-width: 60px !important;
  min-height: 60px !important;
}

/* Override any potential hiding styles */
elevenlabs-convai * {
  visibility: visible !important;
  opacity: 1 !important;
}

/* Global performance optimizations */
* {
  /* Prevent layout thrashing */
  box-sizing: border-box;
}

/* Optimize images for faster loading */
img {
  transform: translateZ(0);
  backface-visibility: hidden;
  /* Prevent layout shifts */
  max-width: 100%;
  height: auto;
}

/* Optimize animations for 60fps */
@media (prefers-reduced-motion: no-preference) {
  * {
    animation-duration: 0.3s !important;
    animation-timing-function: ease-out !important;
  }
}

/* Force hardware acceleration for smooth scrolling */
section, main, div[class*="container"] {
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* Mobile-specific fixes to prevent animation conflicts and improve performance */
@media (max-width: 768px) {
  /* Disable conflicting animations on mobile */
  .animate-heartbeat {
    animation: none !important;
  }

  /* Ensure hero section doesn't interfere with header */
  .hero-section {
    isolation: isolate;
    contain: layout style;
  }

  /* Prevent any transform conflicts */
  .hero-section * {
    will-change: auto;
  }

  /* Stabilize loading dots only */
  .animate-bounce {
    animation-duration: 1s !important;
  }

  /* Prevent horizontal overflow on homepage */
  body {
    overflow-x: hidden !important;
    position: relative;
  }

  /* Stabilize header on homepage */
  header {
    position: fixed !important;
    width: 100vw !important;
    max-width: 100vw !important;
    left: 0 !important;
    right: 0 !important;
    transform: translateZ(0) !important;
    backface-visibility: hidden !important;
    contain: layout style;
  }

  /* Ensure container doesn't overflow */
  .container {
    max-width: 100vw !important;
    overflow-x: hidden !important;
  }

  /* Reduce complex animations on mobile for better performance */
  .animate-pulse {
    animation-duration: 1.5s !important;
  }

  .animate-ping {
    animation: none !important;
  }

  /* Optimize scroll performance on mobile */
  * {
    -webkit-overflow-scrolling: touch;
  }

  /* Reduce motion for better performance */
  .motion-reduce {
    animation: none !important;
    transition: none !important;
  }

  /* Force GPU acceleration for smooth scrolling */
  section, main, article {
    transform: translate3d(0, 0, 0);
    backface-visibility: hidden;
    perspective: 1000px;
  }

  /* Optimize image rendering on mobile */
  img {
    image-rendering: optimizeSpeed;
    image-rendering: -webkit-optimize-contrast;
  }
}

.bg-gray-50 {
  @apply glass;
}

.bg-gray-100 {
  @apply glass-dark;
}

.text-gray-700 {
  @apply text-gray-300;
}

.text-gray-600 {
  @apply text-gray-400;
}

.text-gray-900 {
  @apply text-white;
}

.shadow-md {
  @apply shadow-black/20;
}

.shadow-lg {
  @apply shadow-black/30;
}

.border-gray-300 {
  @apply border-zinc-700;
}

.hover\:bg-gray-100:hover {
  @apply hover:bg-zinc-800;
}

.bg-primary-50 {
  @apply bg-zinc-900;
}

.bg-primary-100 {
  @apply bg-zinc-800;
}

.text-primary-800 {
  @apply text-white;
}

.text-primary-600 {
  @apply text-gray-300;
}