import React from 'react';
import Hero from '../components/home/<USER>';
import StatsSection from '../components/home/<USER>';
import AboutSection from '../components/home/<USER>';
import ProgramsSection from '../components/home/<USER>';
import NewsEventsSection from '../components/home/<USER>';
import TestimonialsSection from '../components/home/<USER>';
import GalleryPreview from '../components/home/<USER>';
import CTASection from '../components/home/<USER>';
import SectionDivider from '../components/common/SectionDivider';

const HomePage: React.FC = () => {
  return (
    <>
      <Hero />
      <SectionDivider position="bottom" />

      <StatsSection />
      <SectionDivider position="bottom" flip={true} />

      <AboutSection />
      <SectionDivider position="bottom" flip={true} />

      <ProgramsSection />
      <SectionDivider position="bottom" />

      <NewsEventsSection />
      <SectionDivider position="bottom" flip={true} />

      <GalleryPreview />
      <SectionDivider position="bottom" />

      <TestimonialsSection />
      <SectionDivider position="bottom" flip={true} />

      <CTASection />
    </>
  );
};

export default HomePage;