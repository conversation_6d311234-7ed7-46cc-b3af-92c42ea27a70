name: Update Sitemap Daily

on:
  # Run daily at 2 AM UTC
  schedule:
    - cron: '0 2 * * *'
  
  # Allow manual trigger
  workflow_dispatch:
  
  # Run on push to main branch
  push:
    branches: [ main, master ]
    paths:
      - 'scripts/generate-sitemap.js'
      - 'package.json'
      - '.github/workflows/update-sitemap.yml'

jobs:
  update-sitemap:
    runs-on: ubuntu-latest
    
    permissions:
      contents: write
      
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
        
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci --only=production
      
    - name: Generate updated sitemap
      run: npm run generate-sitemap
      
    - name: Check for changes
      id: verify-changed-files
      run: |
        if [ -n "$(git status --porcelain)" ]; then
          echo "changed=true" >> $GITHUB_OUTPUT
        else
          echo "changed=false" >> $GITHUB_OUTPUT
        fi
        
    - name: Commit and push changes
      if: steps.verify-changed-files.outputs.changed == 'true'
      run: |
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Action"
        git add public/sitemap.xml public/robots.txt public/llms.txt
        git commit -m "🤖 Auto-update sitemap and SEO files - $(date +'%Y-%m-%d')" || exit 0
        git push
        
    - name: Notify success
      if: steps.verify-changed-files.outputs.changed == 'true'
      run: |
        echo "✅ Sitemap updated successfully on $(date +'%Y-%m-%d %H:%M:%S UTC')"
        echo "📊 Updated files:"
        echo "  - sitemap.xml ($(wc -l < public/sitemap.xml) lines)"
        echo "  - robots.txt ($(wc -l < public/robots.txt) lines)"
        echo "  - llms.txt ($(wc -l < public/llms.txt) lines)"
        
    - name: No changes detected
      if: steps.verify-changed-files.outputs.changed == 'false'
      run: |
        echo "ℹ️ No changes detected in sitemap files"
        echo "📅 Last check: $(date +'%Y-%m-%d %H:%M:%S UTC')"

  # Optional: Ping search engines about sitemap updates
  notify-search-engines:
    runs-on: ubuntu-latest
    needs: update-sitemap
    if: needs.update-sitemap.outputs.changed == 'true'
    
    steps:
    - name: Ping Google
      run: |
        curl -s "https://www.google.com/ping?sitemap=https://stlouisdemojhs.com/sitemap.xml" || true
        echo "🔔 Pinged Google about sitemap update"
        
    - name: Ping Bing
      run: |
        curl -s "https://www.bing.com/ping?sitemap=https://stlouisdemojhs.com/sitemap.xml" || true
        echo "🔔 Pinged Bing about sitemap update"
